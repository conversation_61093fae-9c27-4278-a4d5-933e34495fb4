<?php

namespace App\Livewire\Billing;

use App\Exports\InvoiceReportExport;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Traits\HasPermissions;
use App\Models\WaterRate;
use App\Services\InvoiceGenerationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class UnifiedInvoiceManager extends Component
{
    use HasPermissions, WithFileUploads, WithPagination;

    // Mode management
    public $mode = 'list'; // list, create, edit

    public $invoiceId;

    // List view properties
    public string $status = '';

    public string $estate_id = '';

    public string $date_from = '';

    public string $date_to = '';

    public string $search = '';

    public array $filters = [];

    // Form properties
    public $house_id;

    public $period_start;

    public $period_end;

    public $current_reading;

    public $previous_reading;

    public $consumption;

    public $amount;

    public $notes;

    public $invoice_status = 'pending';

    // Collections
    public $houses = [];

    public $waterRates = [];

    // Report generation properties
    public $showReportModal = false;

    public $report_type = 'summary';

    public $report_date_from;

    public $report_date_to;

    public $report_status = 'all';

    public $report_estate_id;

    // Bulk actions
    public $selectedInvoices = [];

    public $selectAll = false;

    public $bulkAction = '';

    protected $queryString = ['filters'];

    protected function rules()
    {
        return [
            'house_id' => 'required|exists:houses,id',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after:period_start',
            'current_reading' => 'required|numeric|min:0',
            'previous_reading' => 'required|numeric|min:0',
            'consumption' => 'required|numeric|min:0',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'invoice_status' => 'required|in:draft,pending,sent,paid,overdue,cancelled',
        ];
    }

    protected $messages = [
        'house_id.required' => 'Please select a house.',
        'house_id.exists' => 'The selected house does not exist.',
        'period_start.required' => 'Period start date is required.',
        'period_end.required' => 'Period end date is required.',
        'period_end.after' => 'Period end must be after period start.',
        'current_reading.required' => 'Current reading is required.',
        'current_reading.numeric' => 'Current reading must be a number.',
        'current_reading.min' => 'Current reading cannot be negative.',
        'previous_reading.required' => 'Previous reading is required.',
        'previous_reading.numeric' => 'Previous reading must be a number.',
        'previous_reading.min' => 'Previous reading cannot be negative.',
        'consumption.required' => 'Consumption is required.',
        'consumption.numeric' => 'Consumption must be a number.',
        'consumption.min' => 'Consumption cannot be negative.',
        'amount.required' => 'Amount is required.',
        'amount.numeric' => 'Amount must be a number.',
        'amount.min' => 'Amount cannot be negative.',
        'invoice_status.required' => 'Status is required.',
        'invoice_status.in' => 'Invalid status selected.',
    ];

    public function mount()
    {
        $this->authorizeAccess();
        $this->syncFiltersFromProperties();
        $this->setReportDefaults();
        $this->loadCollections();
    }

    private function authorizeAccess()
    {
        // Check if user has any invoice management permission
        $invoicePermissions = [
            'invoices.view_all',
            'invoices.view_assigned',
            'invoices.view_own',
            'invoices.create',
            'invoices.edit',
            'invoices.delete',
        ];

        if (! Auth::user() || ! Auth::user()->hasAnyPermission($invoicePermissions)) {
            abort(403, 'Unauthorized access to invoice management');
        }
    }

    private function syncFiltersFromProperties()
    {
        $this->filters = [
            'status' => $this->status,
            'estate_id' => $this->estate_id,
            'house_id' => $this->house_id,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'search' => $this->search,
        ];
    }

    private function setReportDefaults()
    {
        $this->report_date_from = now()->startOfMonth()->format('Y-m-d');
        $this->report_date_to = now()->endOfMonth()->format('Y-m-d');
    }

    private function loadCollections()
    {
        // Load houses based on user permissions
        if ($this->hasPermission('houses.view_all')) {
            $this->houses = House::with('estate')->orderBy('house_number')->get();
        } elseif ($this->hasPermission('houses.view_assigned')) {
            $user = Auth::user();
            $this->houses = $user->assignedEstates()->flatMap(fn ($estate) => $estate->houses)->sortBy('house_number')->values();
        } else {
            $this->houses = collect();
        }

        // Load water rates if user has permission
        $this->waterRates = $this->hasPermission('water_rates.view') ? WaterRate::active()->get() : collect();
    }

    // Mode management
    public function setMode($mode, $invoiceId = null)
    {
        // Check permissions for mode changes
        if ($mode === 'create' && ! $this->hasPermission('invoices.create')) {
            session()->flash('error', 'You do not have permission to create invoices.');

            return;
        }

        if ($mode === 'edit' && ! $this->hasPermission('invoices.edit')) {
            session()->flash('error', 'You do not have permission to edit invoices.');

            return;
        }

        $this->mode = $mode;
        $this->invoiceId = $invoiceId;

        if ($mode === 'create') {
            $this->resetForm();
            $this->setDefaultValues();
        } elseif ($mode === 'edit' && $invoiceId) {
            $this->loadInvoice($invoiceId);
        }
    }

    public function resetToList()
    {
        $this->mode = 'list';
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->reset([
            'house_id', 'period_start', 'period_end', 'current_reading',
            'previous_reading', 'consumption', 'amount', 'notes', 'invoice_status',
        ]);
    }

    private function setDefaultValues()
    {
        $this->period_start = now()->startOfMonth()->format('Y-m-d');
        $this->period_end = now()->endOfMonth()->format('Y-m-d');
        $this->invoice_status = 'pending';
    }

    private function loadInvoice($invoiceId)
    {
        $invoice = Invoice::findOrFail($invoiceId);
        $this->house_id = $invoice->house_id;
        $this->period_start = $invoice->period_start->format('Y-m-d');
        $this->period_end = $invoice->period_end->format('Y-m-d');
        $this->current_reading = $invoice->current_reading;
        $this->previous_reading = $invoice->previous_reading;
        $this->consumption = $invoice->consumption;
        $this->amount = $invoice->amount;
        $this->notes = $invoice->notes;
        $this->invoice_status = $invoice->status;
    }

    // Form calculations
    public function updated($propertyName)
    {
        if (in_array($propertyName, ['current_reading', 'previous_reading'])) {
            $this->calculateConsumption();
        }
    }

    public function calculateConsumption()
    {
        if ($this->current_reading && $this->previous_reading) {
            $this->consumption = max(0, $this->current_reading - $this->previous_reading);
            $this->calculateAmount();
        }
    }

    public function calculateAmount()
    {
        if ($this->consumption > 0) {
            $invoiceGenerationService = new InvoiceGenerationService;
            $this->amount = $invoiceGenerationService->calculateAmount($this->consumption);
        }
    }

    public function autoCalculateFromHouse()
    {
        if ($this->house_id) {
            $house = House::find($this->house_id);

            // Get the last reading for this house
            $lastReading = $house->meterReadings()
                ->orderBy('reading_date', 'desc')
                ->first();

            if ($lastReading) {
                $this->previous_reading = $lastReading->reading;
                $this->period_start = $lastReading->reading_date->format('Y-m-d');
            }
        }
    }

    // Form actions
    public function save()
    {
        $this->validate();

        $invoiceData = [
            'house_id' => $this->house_id,
            'period_start' => $this->period_start,
            'period_end' => $this->period_end,
            'current_reading' => $this->current_reading,
            'previous_reading' => $this->previous_reading,
            'consumption' => $this->consumption,
            'amount' => $this->amount,
            'notes' => $this->notes,
            'status' => $this->invoice_status,
        ];

        if ($this->mode === 'create') {
            $invoice = Invoice::create($invoiceData);
            $message = 'Invoice created successfully.';
        } else {
            $invoice = Invoice::findOrFail($this->invoiceId);
            $invoice->update($invoiceData);
            $message = 'Invoice updated successfully.';
        }

        session()->flash('message', $message);
        $this->resetToList();
    }

    public function saveAsDraft()
    {
        $this->invoice_status = 'draft';

        return $this->save();
    }

    public function submitForApproval()
    {
        $this->invoice_status = 'pending';

        return $this->save();
    }

    // List view data
    public function getInvoicesProperty()
    {
        $query = Invoice::with(['house.estate', 'meterReading']);

        // Apply permission-based filtering
        $query = $this->applyInvoiceScope($query);

        return $query->when($this->status, fn ($query) => $query->where('status', $this->status))
            ->when($this->estate_id, fn ($query) => $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estate_id);
            }))
            ->when($this->house_id, fn ($query) => $query->where('house_id', $this->house_id))
            ->when($this->date_from, fn ($query) => $query->where('billing_period_start', '>=', $this->date_from))
            ->when($this->date_to, fn ($query) => $query->where('billing_period_end', '<=', $this->date_to))
            ->when($this->search, fn ($query) => $query->where(function ($q): void {
                $q->where('invoice_number', 'like', '%'.$this->search.'%')
                    ->orWhereHas('house', function ($q): void {
                        $q->where('house_number', 'like', '%'.$this->search.'%');
                    });
            }))
            ->orderBy('created_at', 'desc')
            ->paginate(20);
    }

    public function getEstatesProperty()
    {
        if ($this->hasPermission('estates.view_all')) {
            return Estate::orderBy('name')->get();
        } elseif ($this->hasPermission('estates.view_assigned')) {
            return Auth::user()->assignedEstates()->orderBy('name')->get();
        } else {
            return collect();
        }
    }

    public function getHousesProperty()
    {
        if ($this->estate_id !== '' && $this->estate_id !== '0') {
            return House::where('estate_id', $this->estate_id)->orderBy('house_number')->get();
        }

        return collect();
    }

    public function getStatisticsProperty()
    {
        $baseQuery = Invoice::query();

        if ($this->estate_id !== '' && $this->estate_id !== '0') {
            $baseQuery->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estate_id);
            });
        }

        if ($this->date_from && $this->date_to) {
            $baseQuery->whereBetween('created_at', [$this->date_from, $this->date_to]);
        }

        return [
            'total_invoices' => (clone $baseQuery)->count(),
            'total_amount' => (clone $baseQuery)->sum('amount'),
            'paid_invoices' => (clone $baseQuery)->where('status', 'paid')->count(),
            'paid_amount' => (clone $baseQuery)->where('status', 'paid')->sum('amount'),
            'pending_invoices' => (clone $baseQuery)->where('status', 'pending')->count(),
            'overdue_invoices' => (clone $baseQuery)->where('status', 'overdue')->count(),
            'overdue_amount' => (clone $baseQuery)->where('status', 'overdue')->sum('amount'),
        ];
    }

    // Filter methods
    public function updatedEstateId($value)
    {
        $this->house_id = '';
        $this->syncFilters();
    }

    public function updatedStatus($value)
    {
        $this->syncFilters();
    }

    public function updatedHouseId($value)
    {
        $this->syncFilters();
    }

    public function updatedDateFrom($value)
    {
        $this->syncFilters();
    }

    public function updatedDateTo($value)
    {
        $this->syncFilters();
    }

    public function updatedSearch($value)
    {
        $this->syncFilters();
    }

    private function syncFilters()
    {
        $this->filters = [
            'status' => $this->status,
            'estate_id' => $this->estate_id,
            'house_id' => $this->house_id,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'search' => $this->search,
        ];
    }

    public function resetFilters()
    {
        $this->reset(['status', 'estate_id', 'house_id', 'date_from', 'date_to', 'search']);
        $this->syncFilters();
    }

    // Bulk actions
    public function updatedSelectAll($value)
    {
        $this->selectedInvoices = $value ? $this->invoices->pluck('id')->toArray() : [];
    }

    public function performBulkAction()
    {
        if (empty($this->selectedInvoices) || empty($this->bulkAction)) {
            return;
        }

        $invoices = Invoice::whereIn('id', $this->selectedInvoices)->get();

        switch ($this->bulkAction) {
            case 'delete':
                $this->bulkDelete($invoices);
                break;
            case 'mark_sent':
                $this->bulkMarkSent($invoices);
                break;
            case 'mark_paid':
                $this->bulkMarkPaid($invoices);
                break;
            case 'mark_overdue':
                $this->bulkMarkOverdue($invoices);
                break;
        }

        $this->reset(['selectedInvoices', 'selectAll', 'bulkAction']);
        session()->flash('message', 'Bulk action completed successfully!');
    }

    private function bulkDelete($invoices)
    {
        foreach ($invoices as $invoice) {
            if ($this->canDeleteInvoice($invoice)) {
                $invoice->delete();
            }
        }
    }

    private function bulkMarkSent($invoices)
    {
        $invoices->each->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    private function bulkMarkPaid($invoices)
    {
        $invoices->each->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }

    private function bulkMarkOverdue($invoices)
    {
        $invoices->where('status', 'sent')->each->update([
            'status' => 'overdue',
        ]);
    }

    // Report generation
    public function generateReport()
    {
        $this->validate([
            'report_type' => 'required|in:summary,detailed,overdue,payments',
            'report_date_from' => 'required|date',
            'report_date_to' => 'required|date|after_or_equal:report_date_from',
            'report_status' => 'required|in:all,draft,pending,sent,paid,overdue,cancelled',
            'report_estate_id' => 'nullable|exists:estates,id',
        ]);

        $builder = Invoice::with(['house.estate', 'waterRate']);

        // Apply date filter
        $startDate = Carbon::parse($this->report_date_from);
        $endDate = Carbon::parse($this->report_date_to);
        $builder->whereBetween('created_at', [$startDate, $endDate]);

        // Apply status filter
        if ($this->report_status !== 'all') {
            if ($this->report_status === 'overdue') {
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
            } else {
                $builder->where('status', $this->report_status);
            }
        }

        // Apply estate filter
        if ($this->report_estate_id) {
            $builder->whereHas('house.estate', function ($q): void {
                $q->where('id', $this->report_estate_id);
            });
        }

        // Apply report type specific filters
        switch ($this->report_type) {
            case 'overdue':
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
                break;
            case 'payments':
                $builder->where('status', 'paid')
                    ->whereNotNull('paid_at');
                break;
        }

        $invoices = $builder->orderBy('created_at', 'desc')->get();

        $this->showReportModal = false;

        return Excel::download(
            new InvoiceReportExport($invoices),
            'invoice-report-'.$this->report_type.'-'.now()->format('Y-m-d').'.xlsx'
        );
    }

    // Individual actions
    public function delete($invoiceId)
    {
        $invoice = Invoice::findOrFail($invoiceId);

        if (! $this->canDeleteInvoice($invoice)) {
            session()->flash('error', 'Cannot delete this invoice.');

            return;
        }

        $invoice->delete();
        session()->flash('message', 'Invoice deleted successfully!');
    }

    private function canDeleteInvoice($invoice)
    {
        // Check if user has permission to delete this invoice
        if (! $this->hasPermission('invoices.delete_all') &&
            (! $this->hasPermission('invoices.delete_assigned') || ! $this->canAccessEstate($invoice->house->estate_id))) {
            return false;
        }

        // Check if invoice has payments
        if ($invoice->payments()->count() > 0) {
            return false;
        }

        // Check if invoice has been sent
        return ! $invoice->sent_at;
    }

    /**
     * Apply permission-based scope to invoice query
     */
    private function applyInvoiceScope($query)
    {
        $user = Auth::user();

        // If user can view all invoices, no additional filtering needed
        if ($this->hasPermission('invoices.view_all')) {
            return $query;
        }

        // If user can view assigned invoices, filter by assigned estates
        if ($this->hasPermission('invoices.view_assigned')) {
            $assignedEstateIds = $user->assignedEstates()->pluck('id');

            return $query->whereHas('house.estate', function ($q) use ($assignedEstateIds): void {
                $q->whereIn('id', $assignedEstateIds);
            });
        }

        // If user can only view own invoices, filter by user's houses
        if ($this->hasPermission('invoices.view_own')) {
            $userHouseIds = $user->contacts()->pluck('house_id');

            return $query->whereIn('house_id', $userHouseIds);
        }

        // If no specific permission, return empty query
        return $query->whereRaw('1 = 0');
    }

    public function render()
    {
        return view('livewire.billing.unified-invoice-manager', [
            'invoices' => $this->invoices,
            'estates' => $this->estates,
            'houses' => $this->houses,
            'statistics' => $this->statistics,
            'mode' => $this->mode,
        ]);
    }
}
