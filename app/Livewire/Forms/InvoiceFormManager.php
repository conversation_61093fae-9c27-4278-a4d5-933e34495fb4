<?php

namespace App\Livewire\Forms;

use App\Exports\InvoiceReportExport;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Services\InvoiceGenerationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class InvoiceFormManager extends UnifiedFormManager
{
    public $estates = [];

    public $houses = [];

    public $estateFilter = '';

    public $statusFilter = '';

    public $dateFromFilter = '';

    public $dateToFilter = '';

    // Report generation properties
    public $showReportModal = false;

    public $reportType = 'summary';

    public $reportDateFrom;

    public $reportDateTo;

    public $reportStatus = 'all';

    public $reportEstateId;

    public $formDefaults = [
        'house_id' => '',
        'period_start' => '',
        'period_end' => '',
        'current_reading' => '',
        'previous_reading' => '',
        'consumption' => '',
        'amount' => '',
        'notes' => '',
        'status' => 'pending',
    ];

    protected function getModelClass()
    {
        return Invoice::class;
    }

    protected function getValidationRules()
    {
        return [
            'form.house_id' => 'required|exists:houses,id',
            'form.period_start' => 'required|date',
            'form.period_end' => 'required|date|after:form.period_start',
            'form.current_reading' => 'required|numeric|min:0',
            'form.previous_reading' => 'required|numeric|min:0',
            'form.consumption' => 'required|numeric|min:0',
            'form.amount' => 'required|numeric|min:0',
            'form.notes' => 'nullable|string|max:1000',
            'form.status' => 'required|in:draft,pending,sent,paid,overdue,cancelled',
        ];
    }

    protected function getFormFields()
    {
        return [
            'house_id' => ['type' => 'select', 'label' => 'House', 'required' => true, 'options' => $this->houses],
            'period_start' => ['type' => 'date', 'label' => 'Period Start', 'required' => true],
            'period_end' => ['type' => 'date', 'label' => 'Period End', 'required' => true],
            'current_reading' => ['type' => 'number', 'label' => 'Current Reading', 'required' => true],
            'previous_reading' => ['type' => 'number', 'label' => 'Previous Reading', 'required' => true],
            'consumption' => ['type' => 'number', 'label' => 'Consumption', 'required' => true, 'readonly' => true],
            'amount' => ['type' => 'number', 'label' => 'Amount', 'required' => true, 'readonly' => true],
            'notes' => ['type' => 'textarea', 'label' => 'Notes'],
            'status' => ['type' => 'select', 'label' => 'Status', 'required' => true, 'options' => [
                'draft' => 'Draft',
                'pending' => 'Pending',
                'sent' => 'Sent',
                'paid' => 'Paid',
                'overdue' => 'Overdue',
                'cancelled' => 'Cancelled',
            ]],
        ];
    }

    protected function prepareSaveData()
    {
        return $this->form;
    }

    protected function loadEntityData($entity)
    {
        $this->form = [
            'house_id' => $entity->house_id,
            'period_start' => $entity->period_start->format('Y-m-d'),
            'period_end' => $entity->period_end->format('Y-m-d'),
            'current_reading' => $entity->current_reading,
            'previous_reading' => $entity->previous_reading,
            'consumption' => $entity->consumption,
            'amount' => $entity->amount,
            'notes' => $entity->notes ?? '',
            'status' => $entity->status,
        ];
    }

    protected function getListView()
    {
        return 'livewire.forms.invoice-form-manager';
    }

    protected function getFormView()
    {
        return 'livewire.forms.invoice-form';
    }

    protected function getSearchableFields()
    {
        return ['invoice_number', 'notes'];
    }

    protected function applySearch($query)
    {
        return $query->where(function ($q): void {
            $q->where('invoice_number', 'like', '%'.$this->search.'%')
                ->orWhere('notes', 'like', '%'.$this->search.'%')
                ->orWhereHas('house', function ($q): void {
                    $q->where('house_number', 'like', '%'.$this->search.'%');
                });
        });
    }

    protected function applyFilter($query, $field, $value)
    {
        return match ($field) {
            'estate_id' => $query->whereHas('house', function ($q) use ($value): void {
                $q->where('estate_id', $value);
            }),
            'house_id' => $query->where('house_id', $value),
            'status' => $query->where('status', $value),
            'date_from' => $query->where('period_start', '>=', $value),
            'date_to' => $query->where('period_end', '<=', $value),
            default => parent::applyFilter($query, $field, $value),
        };
    }

    public function getItemsProperty()
    {
        $query = Invoice::with(['house.estate', 'waterRate']);

        // Apply search
        if ($this->search) {
            $query = $this->applySearch($query);
        }

        // Apply filters
        foreach ($this->filters as $field => $value) {
            if (! empty($value)) {
                $query = $this->applyFilter($query, $field, $value);
            }
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(20);
    }

    protected function canDeleteItem($item)
    {
        // Check if user has permission to delete this invoice
        if (! Auth::user()->can('invoices.delete_all') &&
            (! Auth::user()->can('invoices.delete_assigned') || $item->house->estate_id !== Auth::user()->estate_id)) {
            session()->flash('error', 'You do not have permission to delete this invoice.');

            return false;
        }

        // Check if invoice has payments
        if ($item->payments()->count() > 0) {
            session()->flash('error', 'Cannot delete invoice that has payments associated with it.');

            return false;
        }

        // Check if invoice has been sent
        if ($item->sent_at) {
            session()->flash('error', 'Cannot delete invoice that has already been sent.');

            return false;
        }

        return true;
    }

    protected function performCustomBulkAction($items, $action)
    {
        switch ($action) {
            case 'mark_sent':
                $items->each->update([
                    'status' => 'sent',
                    'sent_at' => now(),
                ]);
                break;
            case 'mark_paid':
                $items->each->update([
                    'status' => 'paid',
                    'paid_at' => now(),
                ]);
                break;
            case 'mark_overdue':
                $items->where('status', 'sent')->each->update([
                    'status' => 'overdue',
                ]);
                break;
        }
    }

    protected function getStatistics()
    {
        $baseQuery = Invoice::query();

        // Apply estate filter if set
        if ($this->estateFilter) {
            $baseQuery->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estateFilter);
            });
        }

        // Apply date filters if set
        if ($this->dateFromFilter && $this->dateToFilter) {
            $baseQuery->whereBetween('created_at', [$this->dateFromFilter, $this->dateToFilter]);
        }

        return [
            'total_invoices' => (clone $baseQuery)->count(),
            'total_amount' => (clone $baseQuery)->sum('amount'),
            'paid_invoices' => (clone $baseQuery)->where('status', 'paid')->count(),
            'paid_amount' => (clone $baseQuery)->where('status', 'paid')->sum('amount'),
            'pending_invoices' => (clone $baseQuery)->where('status', 'pending')->count(),
            'overdue_invoices' => (clone $baseQuery)->where('status', 'overdue')->count(),
            'overdue_amount' => (clone $baseQuery)->where('status', 'overdue')->sum('amount'),
        ];
    }

    protected function checkPermissions()
    {
        if (! Auth::user()->can('invoices.view')) {
            abort(403, 'Unauthorized action.');
        }
    }

    public function mount()
    {
        parent::mount();
        $this->loadEstates();
        $this->setReportDefaults();
    }

    protected function loadEstates()
    {
        $user = Auth::user();

        if ($user->can('estates.manage_all')) {
            $this->estates = Estate::orderBy('name')->get();
        } else {
            $this->estates = Estate::where('id', $user->estate_id)->orderBy('name')->get();
        }
    }

    protected function setReportDefaults()
    {
        $this->reportDateFrom = now()->startOfMonth()->format('Y-m-d');
        $this->reportDateTo = now()->endOfMonth()->format('Y-m-d');
    }

    public function updatedFormHouseId($value)
    {
        if ($value) {
            $house = House::find($value);
            if ($house) {
                // Get the last reading for this house
                $lastReading = $house->meterReadings()
                    ->orderBy('reading_date', 'desc')
                    ->first();

                if ($lastReading) {
                    $this->form['previous_reading'] = $lastReading->reading;
                    $this->form['period_start'] = $lastReading->reading_date->format('Y-m-d');
                }
            }
        }

        $this->calculateConsumption();
    }

    public function updatedFormCurrentReading()
    {
        $this->calculateConsumption();
    }

    public function updatedFormPreviousReading()
    {
        $this->calculateConsumption();
    }

    public function calculateConsumption()
    {
        if ($this->form['current_reading'] && $this->form['previous_reading']) {
            $this->form['consumption'] = max(0, $this->form['current_reading'] - $this->form['previous_reading']);
            $this->calculateAmount();
        }
    }

    public function calculateAmount()
    {
        if ($this->form['consumption'] > 0) {
            $invoiceGenerationService = new InvoiceGenerationService;
            $this->form['amount'] = $invoiceGenerationService->calculateAmount($this->form['consumption']);
        }
    }

    public function saveAsDraft()
    {
        $this->form['status'] = 'draft';

        return $this->save();
    }

    public function submitForApproval()
    {
        $this->form['status'] = 'pending';

        return $this->save();
    }

    // Report generation
    public function generateReport()
    {
        $this->validate([
            'reportType' => 'required|in:summary,detailed,overdue,payments',
            'reportDateFrom' => 'required|date',
            'reportDateTo' => 'required|date|after_or_equal:reportDateFrom',
            'reportStatus' => 'required|in:all,draft,pending,sent,paid,overdue,cancelled',
            'reportEstateId' => 'nullable|exists:estates,id',
        ]);

        $builder = Invoice::with(['house.estate', 'waterRate']);

        // Apply date filter
        $startDate = Carbon::parse($this->reportDateFrom);
        $endDate = Carbon::parse($this->reportDateTo);
        $builder->whereBetween('created_at', [$startDate, $endDate]);

        // Apply status filter
        if ($this->reportStatus !== 'all') {
            if ($this->reportStatus === 'overdue') {
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
            } else {
                $builder->where('status', $this->reportStatus);
            }
        }

        // Apply estate filter
        if ($this->reportEstateId) {
            $builder->whereHas('house.estate', function ($q): void {
                $q->where('id', $this->reportEstateId);
            });
        }

        // Apply report type specific filters
        switch ($this->reportType) {
            case 'overdue':
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
                break;
            case 'payments':
                $builder->where('status', 'paid')
                    ->whereNotNull('paid_at');
                break;
        }

        $invoices = $builder->orderBy('created_at', 'desc')->get();

        $this->showReportModal = false;

        return Excel::download(
            new InvoiceReportExport($invoices),
            'invoice-report-'.$this->reportType.'-'.now()->format('Y-m-d').'.xlsx'
        );
    }
}
