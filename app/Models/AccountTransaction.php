<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $house_account_id
 * @property string $transaction_type
 * @property string $reference_type
 * @property int|null $reference_id
 * @property numeric $amount
 * @property numeric $balance_before
 * @property numeric $balance_after
 * @property string $direction
 * @property string|null $description
 * @property string|null $notes
 * @property int|null $user_id
 * @property string|null $payment_method
 * @property string|null $payment_reference
 * @property string|null $receipt_number
 * @property string $status
 * @property string|null $processed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $currency
 * @property string $exchange_rate
 * @property string $transaction_reference
 * @property string|null $batch_reference
 * @property string|null $payment_details
 * @property string|null $reconciliation_data
 * @property string|null $reconciled_at
 * @property int|null $reconciled_by
 * @property-read string $amount_with_sign
 * @property-read string $formatted_amount
 * @property-read \Illuminate\Database\Eloquent\Model|null $reference
 * @property-read \App\Models\HouseAccount $houseAccount
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction credits()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction debits()
 * @method static \Database\Factories\AccountTransactionFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction forReference($referenceType, $referenceId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction processed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereBalanceAfter($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereBalanceBefore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereBatchReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereDirection($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereExchangeRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereHouseAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction wherePaymentDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction wherePaymentReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereProcessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereReceiptNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereReconciledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereReconciledBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereReconciliationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereReferenceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereTransactionReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereTransactionType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountTransaction whereUserId($value)
 *
 * @mixin \Eloquent
 */
class AccountTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'house_account_id',
        'transaction_type',
        'reference_type',
        'reference_id',
        'amount',
        'balance_before',
        'balance_after',
        'description',
        'user_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($transaction): void {
            $transaction->houseAccount->updateLastTransactionDate($transaction->created_at);
        });
    }

    public function houseAccount(): BelongsTo
    {
        return $this->belongsTo(HouseAccount::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2);
    }

    public function getAmountWithSignAttribute(): string
    {
        $sign = in_array($this->transaction_type, ['payment', 'credit_note']) ? '+' : '-';

        return $sign.$this->formatted_amount;
    }

    public function isCredit(): bool
    {
        return in_array($this->transaction_type, ['payment', 'credit_note']);
    }

    public function isDebit(): bool
    {
        return in_array($this->transaction_type, ['invoice', 'adjustment']);
    }

    public function isPending(): bool
    {
        return false; // No status field in current implementation
    }

    public function isProcessed(): bool
    {
        return true; // All transactions are considered processed
    }

    public function isFailed(): bool
    {
        return false; // No status field in current implementation
    }

    public function getReference(): ?Model
    {
        $referenceId = $this->getAttribute('reference_id');
        $referenceType = $this->getAttribute('reference_type');

        if ($referenceId && $referenceType) {
            $modelClass = 'App\\Models\\'.$referenceType;
            if (class_exists($modelClass)) {
                return $modelClass::find($referenceId);
            }
        }

        return null;
    }

    public function getReferenceAttribute(): ?Model
    {
        return $this->getReference();
    }

    public function scopeCredits($query)
    {
        return $query->whereIn('transaction_type', ['payment', 'credit_note']);
    }

    public function scopeDebits($query)
    {
        return $query->whereIn('transaction_type', ['invoice', 'adjustment']);
    }

    public function scopeProcessed($query)
    {
        return $query; // All transactions are processed
    }

    public function scopePending($query)
    {
        return $query->whereRaw('1=0'); // No pending transactions
    }

    public function scopeFailed($query)
    {
        return $query->whereRaw('1=0'); // No failed transactions
    }

    public function scopeForReference($query, $referenceType, $referenceId)
    {
        return $query->where('reference_type', $referenceType)
            ->where('reference_id', $referenceId);
    }
}
