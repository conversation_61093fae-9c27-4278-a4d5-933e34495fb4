<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Spatie roles and permissions...');

        // Create roles
        $roles = [
            ['name' => 'admin', 'guard_name' => 'web'],
            ['name' => 'manager', 'guard_name' => 'web'],
            ['name' => 'reviewer', 'guard_name' => 'web'],
            ['name' => 'caretaker', 'guard_name' => 'web'],
            ['name' => 'resident', 'guard_name' => 'web'],
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate($role);
        }

        // Create permissions (comprehensive list for the water management system)
        $permissions = [
            // Dashboard permissions
            ['name' => 'view-admin-dashboard', 'guard_name' => 'web'],
            ['name' => 'view-manager-dashboard', 'guard_name' => 'web'],
            ['name' => 'view-reviewer-dashboard', 'guard_name' => 'web'],
            ['name' => 'view-caretaker-dashboard', 'guard_name' => 'web'],
            ['name' => 'view-resident-dashboard', 'guard_name' => 'web'],

            // Estate permissions
            ['name' => 'estates.view_all', 'guard_name' => 'web'],
            ['name' => 'estates.view_assigned', 'guard_name' => 'web'],
            ['name' => 'estates.manage_all', 'guard_name' => 'web'],
            ['name' => 'estates.manage_assigned', 'guard_name' => 'web'],
            ['name' => 'estates.create', 'guard_name' => 'web'],
            ['name' => 'estates.edit_all', 'guard_name' => 'web'],
            ['name' => 'estates.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'estates.delete', 'guard_name' => 'web'],
            ['name' => 'estates.analytics', 'guard_name' => 'web'],

            // House permissions
            ['name' => 'houses.view_all', 'guard_name' => 'web'],
            ['name' => 'houses.view_assigned', 'guard_name' => 'web'],
            ['name' => 'houses.view_own', 'guard_name' => 'web'],
            ['name' => 'houses.manage_all', 'guard_name' => 'web'],
            ['name' => 'houses.manage_assigned', 'guard_name' => 'web'],
            ['name' => 'houses.create', 'guard_name' => 'web'],
            ['name' => 'houses.edit_all', 'guard_name' => 'web'],
            ['name' => 'houses.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'houses.delete', 'guard_name' => 'web'],

            // Contact permissions
            ['name' => 'contacts.view_all', 'guard_name' => 'web'],
            ['name' => 'contacts.view_assigned', 'guard_name' => 'web'],
            ['name' => 'contacts.view_own', 'guard_name' => 'web'],
            ['name' => 'contacts.manage_all', 'guard_name' => 'web'],
            ['name' => 'contacts.manage_assigned', 'guard_name' => 'web'],
            ['name' => 'contacts.create', 'guard_name' => 'web'],
            ['name' => 'contacts.delete', 'guard_name' => 'web'],
            ['name' => 'contacts.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'contacts.create_assigned', 'guard_name' => 'web'],
            ['name' => 'contacts.view', 'guard_name' => 'web'], // Legacy permission for backward compatibility
            ['name' => 'estates.view', 'guard_name' => 'web'], // Legacy permission for backward compatibility
            ['name' => 'invoices.view', 'guard_name' => 'web'], // Legacy permission for backward compatibility

            // Reading permissions
            ['name' => 'readings.view_all', 'guard_name' => 'web'],
            ['name' => 'readings.view_assigned', 'guard_name' => 'web'],
            ['name' => 'readings.view_own', 'guard_name' => 'web'],
            ['name' => 'readings.create_all', 'guard_name' => 'web'],
            ['name' => 'readings.create_assigned', 'guard_name' => 'web'],
            ['name' => 'readings.edit_all', 'guard_name' => 'web'],
            ['name' => 'readings.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'readings.delete', 'guard_name' => 'web'],
            ['name' => 'readings.review_all', 'guard_name' => 'web'],
            ['name' => 'readings.review_assigned', 'guard_name' => 'web'],
            ['name' => 'readings.approve_all', 'guard_name' => 'web'],
            ['name' => 'readings.approve_assigned', 'guard_name' => 'web'],
            ['name' => 'readings.validate', 'guard_name' => 'web'],

            // Invoice permissions
            ['name' => 'invoices.view_all', 'guard_name' => 'web'],
            ['name' => 'invoices.view_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.view_own', 'guard_name' => 'web'],
            ['name' => 'invoices.generate_all', 'guard_name' => 'web'],
            ['name' => 'invoices.generate_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.create_manual', 'guard_name' => 'web'],
            ['name' => 'invoices.edit_all', 'guard_name' => 'web'],
            ['name' => 'invoices.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.delete', 'guard_name' => 'web'],
            ['name' => 'invoices.send_all', 'guard_name' => 'web'],
            ['name' => 'invoices.send_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.adjust_all', 'guard_name' => 'web'],
            ['name' => 'invoices.adjust_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.export_all', 'guard_name' => 'web'],
            ['name' => 'invoices.export_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.approve_all', 'guard_name' => 'web'],
            ['name' => 'invoices.approve_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.download_own', 'guard_name' => 'web'],
            ['name' => 'invoices.view_payments_own', 'guard_name' => 'web'],
            ['name' => 'invoices.view_adjustments_own', 'guard_name' => 'web'],
            ['name' => 'invoices.view_status_assigned', 'guard_name' => 'web'],
            ['name' => 'invoices.delete_assigned', 'guard_name' => 'web'],

            // Account permissions
            ['name' => 'accounts.view_all', 'guard_name' => 'web'],
            ['name' => 'accounts.view_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.view_own', 'guard_name' => 'web'],
            ['name' => 'accounts.manage_all', 'guard_name' => 'web'],
            ['name' => 'accounts.manage_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.view_balance_all', 'guard_name' => 'web'],
            ['name' => 'accounts.view_balance_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.view_balance_own', 'guard_name' => 'web'],
            ['name' => 'accounts.view_balance_list_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.view_transactions_all', 'guard_name' => 'web'],
            ['name' => 'accounts.view_transactions_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.view_transactions_own', 'guard_name' => 'web'],
            ['name' => 'accounts.create_transaction_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.edit_transaction_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.view_statement_all', 'guard_name' => 'web'],
            ['name' => 'accounts.view_statement_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.view_statement_own', 'guard_name' => 'web'],
            ['name' => 'accounts.export_statement_all', 'guard_name' => 'web'],
            ['name' => 'accounts.export_statement_assigned', 'guard_name' => 'web'],
            ['name' => 'accounts.export_statement_own', 'guard_name' => 'web'],
            ['name' => 'accounts.adjust_balance_assigned', 'guard_name' => 'web'],

            // Payment permissions
            ['name' => 'payments.view_all', 'guard_name' => 'web'],
            ['name' => 'payments.view_assigned', 'guard_name' => 'web'],
            ['name' => 'payments.view_own', 'guard_name' => 'web'],
            ['name' => 'payments.view_history_own', 'guard_name' => 'web'],
            ['name' => 'payments.view_history_assigned', 'guard_name' => 'web'],
            ['name' => 'payments.approve_all', 'guard_name' => 'web'],
            ['name' => 'payments.approve_assigned', 'guard_name' => 'web'],
            ['name' => 'payments.create_assigned', 'guard_name' => 'web'],
            ['name' => 'payments.create_own', 'guard_name' => 'web'],
            ['name' => 'payments.process', 'guard_name' => 'web'],
            ['name' => 'payments.refund', 'guard_name' => 'web'],
            ['name' => 'payments.adjust', 'guard_name' => 'web'],
            ['name' => 'payments.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'payments.export_all', 'guard_name' => 'web'],
            ['name' => 'payments.export_assigned', 'guard_name' => 'web'],
            ['name' => 'payments.export_own', 'guard_name' => 'web'],
            ['name' => 'payments.reconcile_assigned', 'guard_name' => 'web'],
            ['name' => 'payments.view_all_balances', 'guard_name' => 'web'],
            ['name' => 'payments.manage_all', 'guard_name' => 'web'],

            // Report permissions
            ['name' => 'reports.view_all', 'guard_name' => 'web'],
            ['name' => 'reports.view_assigned', 'guard_name' => 'web'],
            ['name' => 'reports.view_own', 'guard_name' => 'web'],
            ['name' => 'reports.generate_all', 'guard_name' => 'web'],
            ['name' => 'reports.generate_assigned', 'guard_name' => 'web'],
            ['name' => 'reports.aging_all', 'guard_name' => 'web'],
            ['name' => 'reports.aging_assigned', 'guard_name' => 'web'],
            ['name' => 'reports.revenue_all', 'guard_name' => 'web'],
            ['name' => 'reports.revenue_assigned', 'guard_name' => 'web'],
            ['name' => 'reports.billing_all', 'guard_name' => 'web'],
            ['name' => 'reports.billing_assigned', 'guard_name' => 'web'],
            ['name' => 'reports.customer_statements_all', 'guard_name' => 'web'],
            ['name' => 'reports.customer_statements_assigned', 'guard_name' => 'web'],
            ['name' => 'reports.financial_all', 'guard_name' => 'web'],
            ['name' => 'reports.financial_assigned', 'guard_name' => 'web'],
            ['name' => 'reports.balance_list_assigned', 'guard_name' => 'web'],

            // Analytics permissions
            ['name' => 'analytics.view_all', 'guard_name' => 'web'],
            ['name' => 'analytics.view_assigned', 'guard_name' => 'web'],
            ['name' => 'analytics.view_own', 'guard_name' => 'web'],

            // Export permissions
            ['name' => 'export.data_all', 'guard_name' => 'web'],
            ['name' => 'export.data_assigned', 'guard_name' => 'web'],
            ['name' => 'export.data_own', 'guard_name' => 'web'],

            // User permissions
            ['name' => 'users.manage_all', 'guard_name' => 'web'], // Required by dashboard route
            ['name' => 'users.manage', 'guard_name' => 'web'], // Added for NavLinkComponent
            ['name' => 'users.view_all', 'guard_name' => 'web'],
            ['name' => 'users.view_assigned', 'guard_name' => 'web'],
            ['name' => 'users.create_all', 'guard_name' => 'web'],
            ['name' => 'users.create_assigned', 'guard_name' => 'web'],
            ['name' => 'users.edit_all', 'guard_name' => 'web'],
            ['name' => 'users.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'users.delete_all', 'guard_name' => 'web'],
            ['name' => 'users.assign_estates', 'guard_name' => 'web'],
            ['name' => 'users.assign_roles', 'guard_name' => 'web'],

            // System permissions
            ['name' => 'system.settings.view', 'guard_name' => 'web'],
            ['name' => 'system.settings.edit', 'guard_name' => 'web'],
            ['name' => 'system.settings.manage', 'guard_name' => 'web'],
            ['name' => 'audit.logs.view', 'guard_name' => 'web'],
            ['name' => 'audit.logs.export', 'guard_name' => 'web'],
            ['name' => 'audit.logs.view_own', 'guard_name' => 'web'],
            ['name' => 'audit.logs.cleanup', 'guard_name' => 'web'],
            ['name' => 'audit.system_logs', 'guard_name' => 'web'],
            ['name' => 'validation.view_rules', 'guard_name' => 'web'],
            ['name' => 'validation.create_rules', 'guard_name' => 'web'],
            ['name' => 'validation.edit_rules', 'guard_name' => 'web'],
            ['name' => 'validation.delete_rules', 'guard_name' => 'web'],
            ['name' => 'validation.run_validation', 'guard_name' => 'web'],

            // WhatsApp permissions
            ['name' => 'whatsapp.settings', 'guard_name' => 'web'],
            ['name' => 'whatsapp.send_all', 'guard_name' => 'web'],
            ['name' => 'whatsapp.send_assigned', 'guard_name' => 'web'],
            ['name' => 'whatsapp.logs.view', 'guard_name' => 'web'],
            ['name' => 'whatsapp.send_invoices_assigned', 'guard_name' => 'web'],

            // Resident permissions
            ['name' => 'resident.portal.access', 'guard_name' => 'web'],
            ['name' => 'resident.inquiries.create', 'guard_name' => 'web'],
            ['name' => 'resident.inquiries.view_assigned', 'guard_name' => 'web'],
            ['name' => 'resident.inquiries.view', 'guard_name' => 'web'],
            ['name' => 'resident.inquiries.respond', 'guard_name' => 'web'],
            ['name' => 'resident.messages.view', 'guard_name' => 'web'],
            ['name' => 'resident.messages.send', 'guard_name' => 'web'],
            ['name' => 'resident.payments.create', 'guard_name' => 'web'],
            ['name' => 'resident.invoices.download', 'guard_name' => 'web'],

            // Rate permissions
            ['name' => 'rates.view_all', 'guard_name' => 'web'],
            ['name' => 'rates.view_assigned', 'guard_name' => 'web'],
            ['name' => 'rates.manage_all', 'guard_name' => 'web'],
            ['name' => 'rates.create', 'guard_name' => 'web'],
            ['name' => 'rates.edit_all', 'guard_name' => 'web'],
            ['name' => 'rates.edit_assigned', 'guard_name' => 'web'],
            ['name' => 'rates.delete', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate($permission);
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        $this->command->info('Spatie roles and permissions seeded successfully!');
    }

    /**
     * Assign permissions to roles.
     */
    private function assignPermissionsToRoles(): void
    {
        // Admin role - gets all permissions
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->syncPermissions(Permission::all());
        }

        // Manager role permissions
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerPermissions = [
                'view-manager-dashboard', // Only manager dashboard access
                'estates.view_assigned', 'estates.manage_assigned', 'estates.edit_assigned', 'estates.analytics',
                'houses.view_assigned', 'houses.manage_assigned', 'houses.edit_assigned',
                'contacts.view_assigned', 'contacts.manage_assigned',
                'readings.view_assigned', 'readings.review_assigned', 'readings.validate',
                'invoices.view_assigned', 'invoices.adjust_assigned', 'invoices.export_assigned',
                'invoices.approve_assigned', 'invoices.send_assigned', 'invoices.generate_assigned',
                'accounts.view_balance_assigned', 'accounts.view_balance_list_assigned',
                'accounts.view_transactions_assigned', 'accounts.view_statement_assigned',
                'accounts.export_statement_assigned', 'accounts.adjust_balance_assigned',
                'reports.view_assigned', 'reports.balance_list_assigned', 'reports.aging_assigned',
                'reports.revenue_assigned', 'reports.billing_assigned', 'analytics.view_assigned', 'export.data_assigned',
                'payments.view_assigned', 'payments.view_history_assigned',
                'payments.create_assigned', 'payments.process', 'payments.adjust',
                'whatsapp.send_assigned', 'whatsapp.logs.view', 'whatsapp.send_invoices_assigned',
                'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
                'resident.messages.view', 'resident.messages.send',
            ];

            $managerRole->syncPermissions($managerPermissions);
        }

        // Reviewer role permissions
        $reviewerRole = Role::where('name', 'reviewer')->first();
        if ($reviewerRole) {
            $reviewerPermissions = [
                'view-reviewer-dashboard', // Only reviewer dashboard access
                'estates.view_assigned',
                'houses.view_assigned', 'houses.edit_assigned',
                'contacts.view_assigned', 'contacts.edit_assigned',
                'readings.view_assigned', 'readings.approve_assigned', 'readings.validate',
                'readings.edit_assigned', 'readings.create_assigned',
                'invoices.view_assigned', 'invoices.generate_assigned', 'invoices.edit_assigned',
                'invoices.send_assigned', 'invoices.export_assigned', 'invoices.approve_assigned',
                'invoices.adjust_assigned', 'invoices.create_manual', 'invoices.delete_assigned',
                'accounts.view_balance_assigned', 'accounts.view_balance_list_assigned',
                'accounts.view_transactions_assigned', 'accounts.view_statement_assigned',
                'accounts.create_transaction_assigned', 'accounts.edit_transaction_assigned',
                'accounts.export_statement_assigned', 'accounts.adjust_balance_assigned',
                'reports.view_assigned', 'reports.balance_list_assigned', 'reports.aging_assigned',
                'reports.revenue_assigned', 'reports.billing_assigned', 'reports.financial_assigned',
                'analytics.view_assigned', 'export.data_assigned',
                'payments.view_assigned', 'payments.approve_assigned', 'payments.create_assigned',
                'payments.edit_assigned', 'payments.process', 'payments.refund', 'payments.adjust',
                'payments.export_assigned', 'payments.reconcile_assigned',
                'whatsapp.send_assigned', 'whatsapp.logs.view', 'whatsapp.send_invoices_assigned',
                'resident.portal.access', 'resident.inquiries.view', 'resident.inquiries.respond',
                'resident.messages.view', 'resident.messages.send',
            ];

            $reviewerRole->syncPermissions($reviewerPermissions);
        }

        // Caretaker role permissions
        $caretakerRole = Role::where('name', 'caretaker')->first();
        if ($caretakerRole) {
            $caretakerPermissions = [
                'view-caretaker-dashboard',
                'estates.view_assigned',
                'houses.view_assigned', 'houses.edit_assigned',
                'contacts.view_assigned', 'contacts.manage_assigned', 'contacts.create_assigned',
                'contacts.edit_assigned',
                'readings.view_assigned', 'readings.create_assigned', 'readings.edit_assigned', 'readings.validate',
                'accounts.view_balance_assigned', 'accounts.view_balance_list_assigned',
                'invoices.view_assigned', 'invoices.view_status_assigned',
                'reports.view_assigned', 'reports.balance_list_assigned',
                'payments.view_assigned', 'payments.view_history_assigned',
                'resident.portal.access', 'resident.inquiries.view_assigned',
            ];

            $caretakerRole->syncPermissions($caretakerPermissions);
        }

        // Resident role permissions
        $residentRole = Role::where('name', 'resident')->first();
        if ($residentRole) {
            $residentPermissions = [
                'view-resident-dashboard',
                'houses.view_own', 'contacts.view_own', 'readings.view_own', 'invoices.view_own',
                'accounts.view_own', 'accounts.view_balance_own', 'accounts.view_transactions_own',
                'accounts.view_statement_own', 'accounts.export_statement_own',
                'invoices.view_own', 'invoices.download_own', 'invoices.view_payments_own',
                'invoices.view_adjustments_own',
                'reports.view_own', 'analytics.view_own', 'export.data_own',
                'payments.view_own', 'payments.view_history_own', 'payments.create_own',
                'resident.portal.access', 'resident.inquiries.create', 'resident.messages.view',
                'resident.payments.create', 'resident.invoices.download',
            ];

            $residentRole->syncPermissions($residentPermissions);
        }
    }
}
