<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function ($user) {
            // Assign admin role by default
            $user->assignRole('admin');
        });
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Set the user's role to manager.
     */
    public function manager(): static
    {
        return $this->afterCreating(function ($user) {
            $user->assignRole('manager');
        });
    }

    /**
     * Set the user's role to caretaker.
     */
    public function caretaker(): static
    {
        return $this->afterCreating(function ($user) {
            $user->assignRole('caretaker');
        });
    }

    /**
     * Set the user's role to reviewer.
     */
    public function reviewer(): static
    {
        return $this->afterCreating(function ($user) {
            $user->assignRole('reviewer');
        });
    }

    /**
     * Set the user's role to resident.
     */
    public function resident(): static
    {
        return $this->afterCreating(function ($user) {
            $user->assignRole('resident');
        });
    }

    /**
     * Set the user's role to admin.
     */
    public function admin(): static
    {
        return $this->afterCreating(function ($user) {
            $user->assignRole('admin');
        });
    }
}
