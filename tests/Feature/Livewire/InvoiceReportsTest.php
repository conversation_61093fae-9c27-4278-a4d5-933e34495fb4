<?php

use App\Livewire\Invoice\InvoiceReports;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WaterRate;
use Livewire\Livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->estate = Estate::factory()->create();
    $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
    $this->waterRate = WaterRate::factory()->create();
});

test('invoice reports component renders', function () {
    $this->actingAs($this->user);

    Livewire::test(InvoiceReports::class)
        ->assertStatus(200)
        ->assertSee('Invoice Reports')
        ->assertSee('Generate Report');
});

test('component has default values', function () {
    $this->actingAs($this->user);

    $testable = Livewire::test(InvoiceReports::class);

    expect($testable->get('report_type'))->toEqual('summary');
    expect($testable->get('status'))->toEqual('all');
    expect($testable->get('date_from'))->not->toBeNull();
    expect($testable->get('date_to'))->not->toBeNull();
});

test('generate report validates input', function () {
    $this->actingAs($this->user);

    Livewire::test(InvoiceReports::class)
        ->set('date_from', '')
        ->set('date_to', '')
        ->call('generateReport')
        ->assertHasErrors(['date_from', 'date_to']);
});

test('generate report validates date range', function () {
    $this->actingAs($this->user);

    Livewire::test(InvoiceReports::class)
        ->set('date_from', '2024-01-15')
        ->set('date_to', '2024-01-10')
        ->call('generateReport')
        ->assertHasErrors(['date_to']);
});

test('generate report with valid data', function () {
    $this->actingAs($this->user);

    // Create test invoice
    Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'status' => 'paid',
        'created_at' => now()->subDays(5),
    ]);

    Livewire::test(InvoiceReports::class)
        ->set('date_from', now()->startOfMonth()->format('Y-m-d'))
        ->set('date_to', now()->endOfMonth()->format('Y-m-d'))
        ->set('report_type', 'summary')
        ->set('status', 'all')
        ->call('generateReport')
        ->assertFileDownloaded();
});

test('generate report filters by status', function () {
    $this->actingAs($this->user);

    // Create invoices with different statuses
    Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'status' => 'paid',
        'created_at' => now()->subDays(5),
    ]);

    Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'status' => 'sent',
        'created_at' => now()->subDays(3),
    ]);

    Livewire::test(InvoiceReports::class)
        ->set('status', 'paid')
        ->call('generateReport')
        ->assertFileDownloaded();
});

test('generate report filters by estate', function () {
    $this->actingAs($this->user);

    $anotherEstate = Estate::factory()->create();
    $anotherHouse = House::factory()->create(['estate_id' => $anotherEstate->id]);

    // Create invoices in different estates
    Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'created_at' => now()->subDays(5),
    ]);

    Invoice::factory()->create([
        'house_id' => $anotherHouse->id,
        'water_rate_id' => $this->waterRate->id,
        'created_at' => now()->subDays(3),
    ]);

    Livewire::test(InvoiceReports::class)
        ->set('estate_id', $this->estate->id)
        ->call('generateReport')
        ->assertFileDownloaded();
});