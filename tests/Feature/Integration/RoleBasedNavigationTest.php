<?php

use App\Models\User;
use Tests\Traits\CreatesTestUsers;
use Tests\Traits\SetsUpSpatiePermissions;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class, CreatesTestUsers::class, SetsUpSpatiePermissions::class);

test('admin user gets admin sidebar on all pages', function (): void {
    $admin = $this->createAdminUser();
    $this->actingAs($admin);

    $response = $this->get(route('admin.dashboard'));
    $response->assertRedirect('/admin/dashboard');

    $response = $this->actingAs($admin)
        ->get('/admin/dashboard');

    $response->assertSee('Admin Dashboard');
    $response->assertSee('Users');
    $response->assertSee('Estate Assignments');
    $response->assertSee('Team Management');
    $response->assertSee('System Settings');
    $response->assertSee('Audit Logs');
});

test('manager user gets manager sidebar on all pages', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $manager = $this->createManagerUser($estate->id);
    $this->actingAs($manager);

    $response = $this->get(route('management.dashboard'));
    $response->assertRedirect('/management/dashboard');

    $response = $this->actingAs($manager)
        ->get('/management/dashboard');

    $response->assertSee('Management Dashboard');
    $response->assertSee('Team');
});

test('reviewer user gets reviewer sidebar on all pages', function (): void {
    $reviewer = $this->createReviewerUser();
    $this->actingAs($reviewer);

    $response = $this->get(route('reviewer.dashboard'));
    $response->assertRedirect('/reviewer/dashboard');

    $response = $this->actingAs($reviewer)
        ->get('/reviewer/dashboard');

    $response->assertSee('Reviewer Dashboard');
    $response->assertSee('Billing');
});

test('caretaker user gets caretaker sidebar on all pages', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $caretaker = $this->createCaretakerUser($estate->id);
    $this->actingAs($caretaker);

    $response = $this->get(route('caretaker.dashboard'));
    $response->assertRedirect('/caretaker/dashboard');

    $response = $this->actingAs($caretaker)
        ->get('/caretaker/dashboard');

    $response->assertSee('Caretaker Dashboard');
    $response->assertSee('Estates');
});

test('resident user gets resident navigation on all pages', function (): void {
    $resident = $this->createResidentUser();
    $this->actingAs($resident);

    $response = $this->get(route('resident.dashboard'));
    $response->assertStatus(200);
    // Should work now
    $response->assertSee('Resident Portal');
    $response->assertSee('Dashboard');
    $response->assertSee('Invoices');
    $response->assertSee('Readings');
    $response->assertSee('Messages');
    $response->assertSee('Contact Us');

    $response = $this->get(route('resident.invoices'));
    $response->assertSee('Resident Portal');
    $response->assertSee('Invoices');
});

test('navigation links are permission appropriate', function (): void {
    // Admin should see admin links
    $admin = $this->createAdminUser();
    $this->actingAs($admin);

    $response = $this->get(route('admin.dashboard'));
    $response->assertSee('User Management');
    $response->assertSee('System Settings');
    $response->assertSee('Audit Logs');

    // Manager should NOT see admin links
    $estate = \App\Models\Estate::factory()->create();
    $manager = $this->createManagerUser($estate->id);
    $this->actingAs($manager);

    $response = $this->get(route('management.dashboard'));
    $response->assertDontSee('User Management');
    $response->assertDontSee('System Settings');
    $response->assertDontSee('Audit Logs');
    $response->assertSee('Team Management');
    $response->assertSee('Reports');
});

test('navigation links respect permissions', function (): void {
    // Reviewer should see billing links
    $reviewer = $this->createReviewerUser();
    $this->actingAs($reviewer);

    $response = $this->get(route('reviewer.dashboard'));
    $response->assertSee('Billing');
    $response->assertSee('Readings');

    // Caretaker should NOT see billing links
    $estate = \App\Models\Estate::factory()->create();
    $caretaker = $this->createCaretakerUser($estate->id);
    $this->actingAs($caretaker);

    $response = $this->get(route('caretaker.dashboard'));
    $response->assertDontSee('Billing');
    $response->assertSee('Readings');
});

test('sidebar components are accessible', function (): void {
    $admin = $this->createAdminUser();
    $this->actingAs($admin);

    $response = $this->get(route('admin.dashboard'));

    $response->assertSee('role="navigation"');
    $response->assertSee('aria-label');
    $response->assertSee('tabindex');
});

test('sidebar components have proper seo structure', function (): void {
    $admin = $this->createAdminUser();
    $this->actingAs($admin);

    $response = $this->get(route('admin.dashboard'));

    $response->assertSee('nav');
    $response->assertSee('ul');
    $response->assertSee('li');
    $response->assertSee('a');
});

test('sidebar components work with javascript disabled', function (): void {
    $admin = $this->createAdminUser();
    $this->actingAs($admin);

    $response = $this->get(route('admin.dashboard'), [], ['HTTP_X-Requested-With' => '']);

    $response->assertStatus(200);
    $response->assertSee('System Overview');
    $response->assertSee('User Management');
});

test('navigation state persists across page requests', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $manager = $this->createManagerUser($estate->id);
    $this->actingAs($manager);

    // Start at dashboard
    $response = $this->get(route('management.dashboard'));
    $response->assertSee('Management Dashboard');

    // Navigate to estates
    $response = $this->get(route('management.estates'));
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');

    // Navigate to houses
    $response = $this->get(route('management.houses'));
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');
    $response->assertSee('Houses');
});

test('user profile displayed consistently across roles', function (): void {
    $users = [
        $this->createAdminUser(['name' => 'Admin User', 'email' => '<EMAIL>']),
        $this->createManagerUser(null, ['name' => 'Manager User', 'email' => '<EMAIL>']),
        $this->createReviewerUser(['name' => 'Reviewer User', 'email' => '<EMAIL>']),
        $this->createCaretakerUser(null, ['name' => 'Caretaker User', 'email' => '<EMAIL>']),
    ];

    foreach ($users as $user) {
        $this->actingAs($user);

        $route = match ($user->role) {
            'admin' => route('admin.dashboard'),
            'manager' => route('management.dashboard'),
            'reviewer' => route('reviewer.dashboard'),
            'caretaker' => route('caretaker.dashboard'),
        };

        $response = $this->get($route);
        $response->assertSee($user->name);
        $response->assertSee($user->email);
        $response->assertSee(substr($user->name, 0, 2)); // Initials
    }
});
