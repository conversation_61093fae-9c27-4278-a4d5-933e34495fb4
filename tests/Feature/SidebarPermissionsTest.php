<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Traits\SetsUpSpatiePermissions;
use Tests\Traits\CreatesTestUsers;

uses(RefreshDatabase::class, SetsUpSpatiePermissions::class, CreatesTestUsers::class);

describe('Sidebar Permissions', function (): void {
    beforeEach(function (): void {
        // Set up Spatie permissions for tests
        $this->setUpSpatiePermissions();

        // Create test users for each role
        $this->admin = $this->createAdminUser();
        $this->manager = $this->createManagerUser();
        $this->reviewer = $this->createReviewerUser();
        $this->caretaker = $this->createCaretakerUser();
        $this->resident = $this->createResidentUser();
    });

    describe('Admin User Sidebar', function (): void {
        it('shows all admin-specific navigation items', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/dashboard')
                ->assertRedirect('/admin/dashboard');

            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Admin Dashboard');
            $response->assertSee('Users');
            $response->assertSee('Estate Assignments');
            $response->assertSee('Team Management');
            $response->assertSee('System Settings');
            $response->assertSee('Audit Logs');
        });

        it('shows admin-level analytics links', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Analytics');
        });

        it('shows admin reports dropdown', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Reports');
            $response->assertSee('Aging Report');
            $response->assertSee('Revenue Report');
        });

        it('hides role-specific items that admin does not need', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            // Admin should not see caretaker-specific record readings link
            $response->assertDontSee('Record Readings');
        });
    });

    describe('Manager User Sidebar', function (): void {
        it('shows manager-specific navigation items', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/dashboard')
                ->assertRedirect('/management/dashboard');

            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('Management Dashboard');
            $response->assertSee('Team');
        });

        it('shows manager-level analytics but not admin analytics', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('Estate Analytics');
            // Should not see general analytics link
            $response->assertDontSee('Analytics');
        });

        it('shows manager reports but not admin reports dropdown', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('Reports');
            $response->assertDontSee('Aging Report'); // This is in admin dropdown
        });

        it('hides admin-specific items', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertDontSee('User Management');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
        });
    });

    describe('Reviewer User Sidebar', function (): void {
        it('shows reviewer-specific navigation items', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/dashboard')
                ->assertRedirect('/reviewer/dashboard');

            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('Reviewer Dashboard');
            $response->assertSee('Billing');
        });

        it('shows reviewer-level analytics but not admin analytics', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('Estate Analytics');
            // Should not see general analytics link
            $response->assertDontSee('Analytics');
        });

        it('shows reviewer reports but not admin reports dropdown', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('Reports');
            $response->assertDontSee('Aging Report'); // This is in admin dropdown
        });

        it('hides admin-specific items', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertDontSee('User Management');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
        });
    });

    describe('Caretaker User Sidebar', function (): void {
        it('shows caretaker-specific navigation items', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/dashboard')
                ->assertRedirect('/caretaker/dashboard');

            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('Caretaker Dashboard');
            $response->assertSee('Estates');
        });

        it('shows caretaker-level analytics but not admin analytics', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('Estate Analytics');
            // Should not see general analytics link
            $response->assertDontSee('Analytics');
        });

        it('shows caretaker reports but not admin reports dropdown', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('Reports');
            $response->assertDontSee('Aging Report'); // This is in admin dropdown
        });

        it('hides admin-specific items', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertDontSee('User Management');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
        });
    });

    describe('Resident User Sidebar', function (): void {
        it('shows resident-specific navigation items', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/dashboard')
                ->assertRedirect('/resident/dashboard');

            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertSee('Resident Portal');
            $response->assertSee('Dashboard');
            $response->assertSee('Invoices');
            $response->assertSee('Readings');
            $response->assertSee('Messages');
            $response->assertSee('Contact Us');
        });

        it('shows resident-level analytics but not admin analytics', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertSee('Analytics');
            // Should not see estate analytics link
            $response->assertDontSee('Estate Analytics');
        });

        it('shows resident reports but not admin reports dropdown', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertSee('Reports');
            $response->assertDontSee('Aging Report'); // This is in admin dropdown
        });

        it('hides admin-specific items', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertDontSee('User Management');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
        });

        it('shows resident-specific links', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertSee('href="'.route('resident.invoices').'"');
        });
    });

    describe('Permission-based Visibility', function (): void {
        it('respects analytics permissions', function (): void {
            // Admin has analytics.view_all - should see both analytics links
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');
            $response->assertSee('Estate Analytics');
            $response->assertSee('Analytics');

            // Manager has analytics.view_assigned - should see estate analytics only
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');
            $response->assertSee('Estate Analytics');
            $response->assertDontSee('Analytics');

            // Resident has analytics.view_own - should see analytics only
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');
            $response->assertDontSee('Estate Analytics');
            $response->assertSee('Analytics');
        });

        it('respects reading permissions', function (): void {
            // Caretaker has readings.create_assigned - should see record readings
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');
            $response->assertSee('Record Readings');

            // Reviewer has readings.review_assigned - should see review readings
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');
            $response->assertSee('Review Readings');

            // Admin has readings.create_all - should see record readings
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');
            $response->assertSee('Record Readings');

            // Manager has readings.review_assigned - should see review readings
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');
            $response->assertSee('Review Readings');

            // Resident has readings.view_own - should see all readings
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');
            $response->assertSee('All Readings');
        });

        it('respects administration permissions', function (): void {
            // Only admin should see administration section
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');
            $response->assertSee('Administration');

            $nonAdminUsers = [$this->manager, $this->reviewer, $this->caretaker, $this->resident];
            foreach ($nonAdminUsers as $nonAdminUser) {
                $response = $this->actingAs($nonAdminUser)
                    ->get('/dashboard');
                $response->assertDontSee('Administration');
            }
        });
    });
});
