<?php

declare(strict_types=1);

use App\Models\Estate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);
uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Tests\Traits\AuthenticationHelpers::class);
uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Tests\Traits\AuthenticationHelpers::class);
uses(\Tests\Traits\SetsUpSpatiePermissions::class);

beforeEach(function () {
    $this->setUpSpatiePermissions();
});
test('guests can access login page', function () {
    $response = $this->get('/login');
    $response->assertStatus(200);
});
test('users can login with valid credentials', function () {
    $user = $this->createAdminUser(['password' => bcrypt('test-password')]);

    $response = $this->post('/login', [
        'email' => $user->email,
        'password' => 'test-password',
    ]);

    $response->assertRedirect('/dashboard');
    $this->assertAuthenticatedAs($user);
});
test('users cannot login with invalid credentials', function () {
    $user = $this->createAdminUser();

    $response = $this->post('/login', [
        'email' => $user->email,
        'password' => 'wrong-password',
    ]);

    $response->assertSessionHasErrors();
    $this->assertGuest();
});
test('users can logout', function () {
    $user = $this->actAsAdmin();

    $response = $this->post('/logout');

    $response->assertRedirect('/');
    $this->assertGuest();
});
test('logout requires authentication', function () {
    $response = $this->post('/logout');
    $response->assertRedirect('/login');
});
test('guests can access registration page', function () {
    $response = $this->get('/register');
    $response->assertStatus(200);
});
test('registration page loads and shows form', function () {
    $response = $this->get('/register');
    $response->assertStatus(200);
    $this->markTestIncomplete('Check Livewire Register component properties and rendered form.');
});
test('password reset request page loads', function () {
    $response = $this->get('/forgot-password');
    $response->assertStatus(200);
    $this->markTestIncomplete('Test ForgotPassword Livewire component.');
});
test('users can access password reset page with valid token', function () {
    $user = $this->createAdminUser();
    $token = \Illuminate\Support\Facades\Password::createToken($user);
    $response = $this->get("/reset-password/{$token}?email={$user->email}");
    $response->assertStatus(200);
    $this->markTestIncomplete('Test ResetPassword Livewire component.');
});
test('unverified users are redirected to verification page', function () {
    $user = User::factory()->create([
        'email_verified_at' => null,
    ]);

    $this->actingAs($user);
    $response = $this->get('/dashboard');
    $response->assertRedirect('/email/verify');
});
test('verified users can access dashboard', function () {
    $user = $this->createResidentUser(['email_verified_at' => now()]);

    $this->actingAs($user);
    $response = $this->get('/dashboard');
    $response->assertRedirect('/resident/dashboard');
});
test('users can request verification email', function () {
    $user = User::factory()->create([
        'email_verified_at' => null,
    ]);

    $this->actingAs($user);
    $response = $this->post('/email/verification-notification');
    $response->assertSessionHas('status', 'verification-link-sent');
});
test('users can verify email with valid link', function () {
    $user = User::factory()->create([
        'email_verified_at' => null,
    ]);

    $this->actingAs($user);
    $response = $this->get('/email/verify/'.$user->id.'/'.sha1($user->email));
    $response->assertRedirect('/dashboard');

    expect($user->fresh()->email_verified_at)->not->toBeNull();
});
test('users are redirected to role specific dashboards based on permissions', function () {
    $testCases = [
        // User with users.manage_all permission goes to admin dashboard
        ['admin', '/admin/dashboard', 'users.manage_all'],
        // User with invoices.view_assigned permission goes to reviewer dashboard
        ['reviewer', '/reviewer/dashboard', 'invoices.view_assigned'],
        // User with estates.view_assigned permission goes to caretaker dashboard
        ['caretaker', '/caretaker/dashboard', 'estates.view_assigned'],
        // User with no specific dashboard permissions goes to main dashboard
        ['user', '/dashboard', null],
    ];

    foreach ($testCases as $testCase) {
        [$roleName, $expectedRedirect, $expectedPermission] = $testCase;

        $user = User::factory()->create();

        if ($expectedPermission) {
            $user->givePermissionTo($expectedPermission);
        }

        if ($roleName !== 'admin') {
            $user->assignRole($roleName);
        }

        $this->actingAs($user);
        $response = $this->get('/dashboard');
        $response->assertRedirect($expectedRedirect);
    }
});
test('users can only access their permission specific dashboards routes', function () {
    // Admin with users.manage_all can access /admin/dashboard
    $admin = $this->createAdminUser();
    $this->actingAs($admin);
    $this->get('/admin/dashboard')->assertStatus(200);

    // Manager with estates.manage_assigned can access /management/dashboard
    $manager = $this->createManagerUser();
    $this->actingAs($manager);
    $this->get('/management/dashboard')->assertStatus(200);

    // Resident with resident.portal.access can access /resident/dashboard
    $resident = $this->createResidentUser();
    $this->actingAs($resident);
    $this->get('/resident/dashboard')->assertStatus(200);

    // Test a route a resident should NOT access
    $this->actingAs($resident);
    $this->get('/admin/dashboard')->assertStatus(403);
});
test('estate assignments restrict access to assigned estates', function () {
    $estate1 = Estate::factory()->create(['code' => 'EST-001', 'name' => 'Estate Alpha']);
    $estate2 = Estate::factory()->create(['code' => 'EST-002', 'name' => 'Estate Beta']);

    $manager1 = $this->createUserWithEstate('manager', $estate1);
    $manager2 = $this->createUserWithEstate('manager', $estate2);

    $this->actingAs($manager1);
    $response = $this->get('/management/estates');
    $response->assertStatus(200);

    $this->markTestIncomplete('Estate listing filtering by assignment needs Livewire component test.');
});
test('user sessions are properly managed', function () {
    $user = $this->actAsAdmin();

    $this->assertAuthenticatedAs($user);
    expect(session()->isStarted())->toBeTrue();
    expect(session()->get(config('auth.session_prefix', 'login').'.'.config('auth.defaults.guard')))->not->toBeNull();

    $response = $this->post('/logout');
    $response->assertRedirect('/');
    $this->assertGuest();
    expect(session()->get(config('auth.session_prefix', 'login').'.'.config('auth.defaults.guard')))->toBeNull();
});
test('multiple sessions can be active', function () {
    $user = $this->createAdminUser();

    $this->post('/login', [
        'email' => $user->email,
        'password' => 'password',
    ]);

    $this->assertAuthenticated();

    $this->post('/logout');
    $this->post('/login', [
        'email' => $user->email,
        'password' => 'password',
    ]);

    $this->assertAuthenticated();
});
test('passwords are properly hashed', function () {
    $user = $this->createAdminUser(['password' => bcrypt('plain-password')]);

    $dbUser = User::find($user->id);

    $this->assertNotEquals('plain-password', $dbUser->password);
    expect(password_verify('plain-password', (string) $dbUser->password))->toBeTrue();
});
test('login page loads and shows form', function () {
    $response = $this->get('/login');
    $response->assertStatus(200);
    $this->markTestIncomplete('Test Login Livewire component properties and rendered form.');
});
test('handles non existent user login attempts gracefully', function () {
    $this->markTestIncomplete('Test non-existent user login in Login Livewire component.');
});
test('handles deactivated user login attempts', function () {
    $this->markTestSkipped('Deactivation mechanism needs clarification (e.g., permission or soft delete).');
});
test('handles expired password reset tokens', function () {
    $this->markTestIncomplete('Test expired token handling in ResetPassword Livewire component.');
});
test('authentication throttling needs livewire component test', function () {
    $this->markTestIncomplete('Test login throttling in Login Livewire component.');
});
test('csrf protection on login form', function () {
    $response = $this->get('/login');
    $response->assertStatus(200);
    $this->markTestIncomplete('Verify CSRF token presence in Login Livewire component rendered form.');
});
test('registration validation needs livewire component test', function () {
    $this->markTestIncomplete('Validate Register Livewire component form submission.');
});
test('registration unique email needs livewire component test', function () {
    $this->markTestIncomplete('Test unique email validation in Register Livewire component.');
});
test('password reset process needs livewire component test', function () {
    $this->markTestIncomplete('Test password reset submission in ResetPassword Livewire component.');
});
test('password reset with invalid token needs livewire component test', function () {
    $this->markTestIncomplete('Test invalid token handling in ResetPassword Livewire component.');
});
