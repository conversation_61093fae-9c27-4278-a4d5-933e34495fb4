<?php

declare(strict_types=1);

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);
uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Tests\Traits\SetsUpSpatiePermissions::class);

beforeEach(function () {
    $this->setUpSpatiePermissions();
    createTestInvoiceData();
});
function createTestInvoiceData(): void
{
    $this->estate = Estate::factory()->create();
    $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
    $this->waterRate = WaterRate::factory()->create(['estate_id' => $this->estate->id]);
    $this->meterReading = MeterReading::factory()->create(['house_id' => $this->house->id]);
    $this->manager = $this->createManagerUser();
    $this->reviewer = $this->createReviewerUser();
}
test('can be submitted for approval', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'draft',
    ]);

    $invoice->submitForApproval($this->manager);

    expect($invoice->fresh()->status)->toEqual('submitted');
    expect($invoice->fresh()->submitted_by)->toEqual($this->manager->id);
    expect($invoice->fresh()->submitted_at)->not->toBeNull();
});
test('can be approved by reviewer', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $this->manager->id,
        'submitted_at' => now(),
    ]);

    $invoice->approve($this->reviewer);

    expect($invoice->fresh()->status)->toEqual('approved');
    expect($invoice->fresh()->approved_by)->toEqual($this->reviewer->id);
    expect($invoice->fresh()->approved_at)->not->toBeNull();
});
test('cannot be approved if not submitted', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'draft',
    ]);

    $this->expectException(\InvalidArgumentException::class);
    $this->expectExceptionMessage('Invoice must be submitted before approval');

    $invoice->approve($this->reviewer);
});
test('can be sent after approval', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'approved',
        'approved_by' => $this->reviewer->id,
        'approved_at' => now(),
        'pdf_path' => 'invoices/test.pdf', // Required for canBeSent()
    ]);

    $invoice->markAsSent();

    expect($invoice->fresh()->status)->toEqual('sent');
    expect($invoice->fresh()->sent_at)->not->toBeNull();
});
test('cannot be sent if not approved', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
    ]);

    $this->expectException(\InvalidArgumentException::class);
    $this->expectExceptionMessage('Invoice must be approved before sending');

    $invoice->markAsSent();
});
test('tracks reminder count and last reminder', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'sent',
        'reminder_count' => 0,
    ]);

    $invoice->recordReminder();

    expect($invoice->fresh()->reminder_count)->toEqual(1);
    expect($invoice->fresh()->last_reminder_at)->not->toBeNull();
});
test('can schedule disconnection', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'overdue',
    ]);

    $disconnectionDate = now()->addDays(7)->startOfDay();
    $invoice->scheduleDisconnection($disconnectionDate);

    expect($invoice->fresh()->disconnection_scheduled)->toEqual($disconnectionDate);
});
test('has relationship with submitter', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $this->manager->id,
    ]);

    expect($invoice->submittedBy)->toBeInstanceOf(User::class);
    expect($invoice->submittedBy->id)->toEqual($this->manager->id);
});
test('has relationship with approver', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'approved',
        'approved_by' => $this->reviewer->id,
    ]);

    expect($invoice->approvedBy)->toBeInstanceOf(User::class);
    expect($invoice->approvedBy->id)->toEqual($this->reviewer->id);
});
test('can check if it can be submitted', function () {
    $draftInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'draft',
    ]);

    $submittedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
    ]);

    expect($draftInvoice->canBeSubmitted())->toBeTrue();
    expect($submittedInvoice->canBeSubmitted())->toBeFalse();
});
test('can check if it can be approved', function () {
    $submittedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
    ]);

    $approvedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'approved',
    ]);

    expect($submittedInvoice->canBeApproved())->toBeTrue();
    expect($approvedInvoice->canBeApproved())->toBeFalse();
});
test('can check if it can be sent', function () {
    $approvedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'approved',
        'pdf_path' => 'invoices/test.pdf', // Required for canBeSent()
    ]);

    $sentInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'sent',
    ]);

    expect($approvedInvoice->canBeSent())->toBeTrue();
    expect($sentInvoice->canBeSent())->toBeFalse();
});
test('returns correct status label', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
    ]);

    expect($invoice->status_label)->toEqual('Submitted');
});
test('returns correct status color', function () {
    $submittedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
    ]);

    $approvedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'approved',
    ]);

    expect($submittedInvoice->status_color)->toEqual('yellow');
    expect($approvedInvoice->status_color)->toEqual('blue');
});
test('can check user permissions for actions', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'draft',
    ]);

    // Test manager can submit
    $this->actingAs($this->manager);
    expect($this->manager->can('invoices.submit_assigned'))->toBeTrue();

    // Test reviewer can approve
    $this->actingAs($this->reviewer);
    expect($this->reviewer->can('invoices.approve_assigned'))->toBeTrue();

    // Test manager cannot approve
    $this->actingAs($this->manager);
    expect($this->manager->can('invoices.approve_assigned'))->toBeFalse();

    // Test reviewer cannot submit
    $this->actingAs($this->reviewer);
    expect($this->reviewer->can('invoices.submit_assigned'))->toBeFalse();
});
test('can check invoice permissions based on status', function () {
    $draftInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'draft',
    ]);

    $submittedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
    ]);

    $approvedInvoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'approved',
    ]);

    // Test manager permissions
    $this->actingAs($this->manager);
    expect($this->manager->can('invoices.create_assigned'))->toBeTrue();
    expect($this->manager->can('invoices.submit_assigned'))->toBeTrue();
    expect($this->manager->can('invoices.approve_assigned'))->toBeFalse();

    // Test reviewer permissions
    $this->actingAs($this->reviewer);
    expect($this->reviewer->can('invoices.approve_assigned'))->toBeTrue();
    expect($this->reviewer->can('invoices.submit_assigned'))->toBeFalse();
});
test('can check invoice access based on user role', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'meter_reading_id' => $this->meterReading->id,
        'status' => 'submitted',
    ]);

    // Test admin access
    $admin = $this->createAdminUser();
    $this->actingAs($admin);
    expect($admin->can('invoices.view_all'))->toBeTrue();

    // Test manager access
    $this->actingAs($this->manager);
    expect($this->manager->can('invoices.view_assigned'))->toBeTrue();

    // Test reviewer access
    $this->actingAs($this->reviewer);
    expect($this->reviewer->can('invoices.view_assigned'))->toBeTrue();

    // Test resident access
    $resident = $this->createResidentUser();
    $this->actingAs($resident);
    expect($resident->can('invoices.view_own'))->toBeTrue();
});
