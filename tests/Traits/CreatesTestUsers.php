<?php

namespace Tests\Traits;

use App\Models\Estate;
use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

trait CreatesTestUsers
{
    /**
     * Create an admin user with all permissions.
     */
    protected function createAdminUser(array $attributes = []): User
    {
        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Admin',
            'email_verified_at' => now(),
        ], $attributes));

        // Assign admin role and all permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $user->assignRole($adminRole);
        $user->givePermissionTo(Permission::all());

        return $user;
    }

    /**
     * Create a manager user with estate management permissions.
     */
    protected function createManagerUser(array $attributes = []): User
    {
        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Manager',
            'email_verified_at' => now(),
        ], $attributes));

        // Assign manager role and permissions
        $managerRole = Role::firstOrCreate(['name' => 'manager', 'guard_name' => 'web']);
        $user->assignRole($managerRole);

        $managerPermissions = [
            'estates.view_assigned', 'estates.manage_assigned', 'estates.edit_assigned',
            'houses.view_assigned', 'houses.manage_assigned', 'houses.edit_assigned',
            'invoices.view_assigned', 'invoices.generate_assigned', 'invoices.approve_assigned',
            'reports.view_assigned', 'analytics.view_assigned'
        ];

        // Ensure permissions exist
        foreach ($managerPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        $user->givePermissionTo($managerPermissions);

        return $user;
    }

    /**
     * Create a reviewer user with invoice review permissions.
     */
    protected function createReviewerUser(array $attributes = []): User
    {
        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Reviewer',
            'email_verified_at' => now(),
        ], $attributes));

        // Assign reviewer role and permissions
        $reviewerRole = Role::firstOrCreate(['name' => 'reviewer', 'guard_name' => 'web']);
        $user->assignRole($reviewerRole);

        $reviewerPermissions = [
            'readings.approve_assigned', 'invoices.approve_assigned',
            'invoices.view_assigned', 'payments.view_assigned'
        ];

        // Ensure permissions exist
        foreach ($reviewerPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        $user->givePermissionTo($reviewerPermissions);

        return $user;
    }

    /**
     * Create a caretaker user with assigned permissions.
     */
    protected function createCaretakerUser(array $attributes = []): User
    {
        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Caretaker',
            'email_verified_at' => now(),
        ], $attributes));

        // Assign caretaker role and permissions
        $caretakerRole = Role::firstOrCreate(['name' => 'caretaker', 'guard_name' => 'web']);
        $user->assignRole($caretakerRole);

        $caretakerPermissions = [
            'readings.create_assigned', 'readings.view_assigned',
            'contacts.manage_assigned', 'houses.view_assigned'
        ];

        // Ensure permissions exist
        foreach ($caretakerPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        $user->givePermissionTo($caretakerPermissions);

        return $user;
    }

    /**
     * Create a resident user with own permissions.
     */
    protected function createResidentUser(array $attributes = []): User
    {
        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Resident',
            'email_verified_at' => now(),
        ], $attributes));

        // Assign resident role and permissions
        $residentRole = Role::firstOrCreate(['name' => 'resident', 'guard_name' => 'web']);
        $user->assignRole($residentRole);

        $residentPermissions = [
            'invoices.view_own', 'accounts.view_own',
            'payments.view_own'
        ];

        // Ensure permissions exist
        foreach ($residentPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        $user->givePermissionTo($residentPermissions);

        return $user;
    }

    /**
     * Create a user with estate assignment.
     */
    protected function createUserWithEstate(string $role, Estate $estate, array $attributes = []): User
    {
        $user = User::factory()->create(array_merge([
            'email_verified_at' => now(),
        ], $attributes));

        $user->assignedEstates()->attach($estate->id, [
            'assigned_from' => now()->toDateString(),
            'is_active' => true,
        ]);

        return $user;
    }

    /**
     * Create users for all roles with estate assignments.
     */
    protected function createAllUsersWithEstates(): array
    {
        $estate = Estate::first() ?? Estate::factory()->create();

        return [
            'admin' => $this->createUserWithEstate('admin', $estate),
            'manager' => $this->createUserWithEstate('manager', $estate),
            'reviewer' => $this->createUserWithEstate('reviewer', $estate),
            'caretaker' => $this->createUserWithEstate('caretaker', $estate),
            'resident' => $this->createUserWithEstate('resident', $estate),
        ];
    }
}
